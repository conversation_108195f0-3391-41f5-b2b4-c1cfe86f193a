# 图片色温计算工具

这是一个用于分析图片色温的Python工具，支持单张图片分析和批量处理。

## 功能特点

- **精确色温计算**: 基于CIE色彩空间和McCamy公式计算色温
- **多种分析方法**: 支持简单平均和亮度加权平均两种算法
- **批量处理**: 支持目录批量分析
- **详细结果**: 提供RGB值、色度坐标、色温值和类型分类
- **标准光源对比**: 与标准光源进行对比分析
- **JSON输出**: 支持将结果导出为JSON格式

## 安装依赖

```bash
pip install pillow numpy
```

## 使用方法

### 1. 分析单张图片

```bash
# 基本用法
python image_color_temperature.py image.jpg

# 使用简单平均方法
python image_color_temperature.py image.jpg --method average

# 静默模式
python image_color_temperature.py image.jpg --quiet
```

### 2. 批量分析目录

```bash
# 批量分析目录中的所有图片
python image_color_temperature.py test_imgs/

# 将结果保存到JSON文件
python image_color_temperature.py test_imgs/ --output results.json

# 使用不同的分析方法
python image_color_temperature.py test_imgs/ --method average --output results.json
```

### 3. 在Python代码中使用

```python
from image_color_temperature import ColorTemperatureAnalyzer, analyze_single_image

# 方法1: 使用便捷函数
result = analyze_single_image('image.jpg', method='weighted', verbose=True)
print(f"色温: {result['color_temperature']}K")

# 方法2: 使用分析器类
analyzer = ColorTemperatureAnalyzer()
result = analyzer.analyze_color_temperature('image.jpg', method='weighted')
print(f"色温类型: {result['temperature_type']}")
```

## 输出结果说明

分析结果包含以下信息：

```json
{
  "image_path": "test.jpg",
  "image_size": "1920x1080",
  "method": "weighted",
  "average_rgb": {
    "r": 0.8235,
    "g": 0.7451,
    "b": 0.6863
  },
  "rgb_255": {
    "r": 210,
    "g": 190,
    "b": 175
  },
  "chromaticity": {
    "x": 0.3456,
    "y": 0.3585
  },
  "color_temperature": 4250,
  "temperature_type": "中性光",
  "closest_standard": {
    "temperature": 4000,
    "difference": 250,
    "rgb": [1.0, 0.794, 0.576]
  }
}
```

### 字段说明

- `color_temperature`: 计算得出的色温值（开尔文）
- `temperature_type`: 色温类型分类
- `average_rgb`: 归一化的平均RGB值（0-1范围）
- `rgb_255`: 标准RGB值（0-255范围）
- `chromaticity`: CIE xy色度坐标
- `closest_standard`: 最接近的标准光源信息

## 色温分类

| 色温范围 | 类型 | 典型光源 |
|---------|------|----------|
| < 2000K | 极暖光 | 烛光 |
| 2000-3000K | 暖光 | 白炽灯 |
| 3000-4000K | 中性暖光 | 暖白LED |
| 4000-5000K | 中性光 | 荧光灯 |
| 5000-6000K | 冷光 | 日光灯 |
| 6000-7000K | 日光 | 正午阳光 |
| 7000-9000K | 冷日光 | 阴天 |
| > 9000K | 极冷光 | 蓝天 |

## 分析方法

### 1. 简单平均 (average)
计算图片中所有像素的RGB平均值，适用于颜色分布均匀的图片。

### 2. 亮度加权平均 (weighted)
根据像素亮度进行加权平均，更重视亮度较高的区域，通常能得到更准确的结果。

## 技术原理

1. **RGB提取**: 从图片中提取平均RGB值
2. **色彩空间转换**: 将RGB转换为CIE XYZ色彩空间
3. **色度计算**: 计算CIE xy色度坐标
4. **色温计算**: 使用McCamy公式将色度坐标转换为相关色温(CCT)

## 测试工具

运行测试脚本验证功能：

```bash
python test_color_temperature.py
```

测试脚本会：
- 创建不同色温的测试图片
- 验证色温计算的准确性
- 测试边界情况
- 分析真实图片（如果存在）

## 注意事项

1. **图片格式**: 支持 JPG、PNG、BMP、TIFF、WebP 格式
2. **性能优化**: 大图片会自动下采样以提高处理速度
3. **准确性**: 色温计算基于图片的整体色调，可能受拍摄条件影响
4. **适用场景**: 适合分析照明条件、白平衡调整、色彩校正等

## 命令行参数

```
positional arguments:
  input                 输入文件或目录路径

optional arguments:
  -h, --help           显示帮助信息
  --output, -o         输出JSON文件路径（仅批量处理时使用）
  --method, -m         分析方法: average=简单平均, weighted=亮度加权平均
  --quiet, -q          静默模式，不打印详细信息
```

## 示例输出

```
=== 图片色温分析结果 ===
文件: test_imgs/sample.jpg
尺寸: 1920x1080
分析方法: weighted
平均RGB: R=210, G=190, B=175
色度坐标: x=0.3456, y=0.3585
色温: 4250K
色温类型: 中性光
最接近标准光源: 4000K (差值: 250K)
```

## 扩展功能

可以基于此工具进行以下扩展：
- 白平衡自动调整
- 色温匹配和校正
- 照明条件分析
- 图片质量评估
- 色彩一致性检查
