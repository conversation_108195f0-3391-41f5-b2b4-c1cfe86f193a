#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片色调分析工具
分析图片的主要色调，支持多种算法和批量处理
"""

import os
import sys
import numpy as np
from PIL import Image
import argparse
import json
import colorsys
from typing import Tuple, Dict, List, Optional
from collections import Counter
import matplotlib.pyplot as plt
import matplotlib.patches as patches


class HueAnalyzer:
    """色调分析器"""
    
    def __init__(self):
        """初始化色调分析器"""
        # 定义主要色调范围 (HSV中的H值，范围0-360度)
        self.hue_ranges = {
            '红色': (0, 15, 345, 360),      # 0-15度和345-360度
            '橙色': (15, 45),               # 15-45度
            '黄色': (45, 75),               # 45-75度
            '黄绿色': (75, 105),            # 75-105度
            '绿色': (105, 135),             # 105-135度
            '青绿色': (135, 165),           # 135-165度
            '青色': (165, 195),             # 165-195度
            '蓝色': (195, 225),             # 195-225度
            '蓝紫色': (225, 255),           # 225-255度
            '紫色': (255, 285),             # 255-285度
            '紫红色': (285, 315),           # 285-315度
            '粉红色': (315, 345),           # 315-345度
        }
    
    def rgb_to_hsv(self, r: float, g: float, b: float) -> Tuple[float, float, float]:
        """
        将RGB值转换为HSV值
        
        Args:
            r, g, b: RGB值 (0-1范围)
            
        Returns:
            tuple: (h, s, v) HSV值，h为0-360度，s和v为0-1范围
        """
        h, s, v = colorsys.rgb_to_hsv(r, g, b)
        return h * 360, s, v  # 将色调转换为度数
    
    def classify_hue(self, hue: float) -> str:
        """
        根据色调值分类颜色
        
        Args:
            hue: 色调值 (0-360度)
            
        Returns:
            str: 颜色名称
        """
        # 处理红色的特殊情况（跨越0度）
        if hue >= 345 or hue <= 15:
            return '红色'
        
        for color_name, ranges in self.hue_ranges.items():
            if color_name == '红色':
                continue  # 已经处理过
            
            if len(ranges) == 2:  # 普通范围
                if ranges[0] <= hue < ranges[1]:
                    return color_name
        
        return '未知'
    
    def calculate_dominant_hue(self, image: Image.Image, sample_ratio: float = 0.1, 
                              saturation_threshold: float = 0.1) -> Dict:
        """
        计算图片的主导色调
        
        Args:
            image: PIL图像对象
            sample_ratio: 采样比例，用于加速处理大图片
            saturation_threshold: 饱和度阈值，低于此值的像素被视为灰色
            
        Returns:
            dict: 包含主导色调信息的字典
        """
        # 转换为RGB模式
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # 如果图片太大，进行下采样
        width, height = image.size
        if sample_ratio < 1.0:
            new_width = int(width * sample_ratio)
            new_height = int(height * sample_ratio)
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # 转换为numpy数组
        img_array = np.array(image, dtype=np.float32) / 255.0
        
        # 计算每个像素的HSV值
        hues = []
        saturations = []
        values = []
        
        for i in range(img_array.shape[0]):
            for j in range(img_array.shape[1]):
                r, g, b = img_array[i, j]
                h, s, v = self.rgb_to_hsv(r, g, b)
                
                # 只考虑饱和度足够高的像素
                if s >= saturation_threshold:
                    hues.append(h)
                    saturations.append(s)
                    values.append(v)
        
        if not hues:
            return {
                'dominant_hue': None,
                'dominant_color': '灰色/无色',
                'hue_distribution': {},
                'average_saturation': 0,
                'average_value': 0,
                'colored_pixel_ratio': 0
            }
        
        # 计算色调分布
        hue_counts = Counter()
        for hue in hues:
            color_name = self.classify_hue(hue)
            hue_counts[color_name] += 1
        
        # 找到主导色调
        dominant_color = hue_counts.most_common(1)[0][0]
        
        # 计算主导色调的平均色调值
        dominant_hues = [h for h in hues if self.classify_hue(h) == dominant_color]
        
        # 处理红色跨越0度的情况
        if dominant_color == '红色':
            # 将大于180度的红色色调转换为负值来计算平均值
            adjusted_hues = []
            for h in dominant_hues:
                if h > 180:
                    adjusted_hues.append(h - 360)
                else:
                    adjusted_hues.append(h)
            avg_hue = np.mean(adjusted_hues)
            if avg_hue < 0:
                avg_hue += 360
        else:
            avg_hue = np.mean(dominant_hues)
        
        # 计算色调分布百分比
        total_colored_pixels = len(hues)
        hue_distribution = {color: count / total_colored_pixels * 100 
                           for color, count in hue_counts.items()}
        
        return {
            'dominant_hue': round(avg_hue, 1),
            'dominant_color': dominant_color,
            'hue_distribution': {k: round(v, 1) for k, v in hue_distribution.items()},
            'average_saturation': round(np.mean(saturations), 3),
            'average_value': round(np.mean(values), 3),
            'colored_pixel_ratio': round(total_colored_pixels / (img_array.shape[0] * img_array.shape[1]) * 100, 1)
        }
    
    def calculate_average_hue(self, image: Image.Image, sample_ratio: float = 0.1) -> Dict:
        """
        计算图片的平均色调
        
        Args:
            image: PIL图像对象
            sample_ratio: 采样比例
            
        Returns:
            dict: 包含平均色调信息的字典
        """
        # 转换为RGB模式
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # 下采样
        if sample_ratio < 1.0:
            width, height = image.size
            new_width = int(width * sample_ratio)
            new_height = int(height * sample_ratio)
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # 转换为numpy数组
        img_array = np.array(image, dtype=np.float32) / 255.0
        
        # 计算平均RGB值
        avg_r = np.mean(img_array[:, :, 0])
        avg_g = np.mean(img_array[:, :, 1])
        avg_b = np.mean(img_array[:, :, 2])
        
        # 转换为HSV
        avg_h, avg_s, avg_v = self.rgb_to_hsv(avg_r, avg_g, avg_b)
        
        # 分类色调
        color_name = self.classify_hue(avg_h)
        
        return {
            'average_hue': round(avg_h, 1),
            'average_saturation': round(avg_s, 3),
            'average_value': round(avg_v, 3),
            'average_color': color_name,
            'average_rgb': {
                'r': round(avg_r, 4),
                'g': round(avg_g, 4),
                'b': round(avg_b, 4)
            }
        }
    
    def analyze_image_hue(self, image_path: str, method: str = 'dominant', 
                         sample_ratio: float = 0.1, saturation_threshold: float = 0.1) -> Dict:
        """
        分析图片的色调
        
        Args:
            image_path: 图片路径
            method: 分析方法 ('dominant' 或 'average')
            sample_ratio: 采样比例
            saturation_threshold: 饱和度阈值
            
        Returns:
            dict: 包含色调分析结果的字典
        """
        try:
            # 打开图片
            with Image.open(image_path) as img:
                # 获取图片信息
                width, height = img.size
                
                # 根据方法分析色调
                if method == 'dominant':
                    hue_info = self.calculate_dominant_hue(img, sample_ratio, saturation_threshold)
                else:  # average
                    hue_info = self.calculate_average_hue(img, sample_ratio)
                
                result = {
                    'image_path': image_path,
                    'image_size': f"{width}x{height}",
                    'method': method,
                    'sample_ratio': sample_ratio
                }
                
                if method == 'dominant':
                    result['saturation_threshold'] = saturation_threshold
                
                result.update(hue_info)
                
                return result
                
        except Exception as e:
            return {
                'image_path': image_path,
                'error': str(e)
            }


def analyze_single_image(image_path: str, method: str = 'dominant', 
                        sample_ratio: float = 0.1, saturation_threshold: float = 0.1,
                        verbose: bool = True) -> Dict:
    """
    分析单张图片的色调
    
    Args:
        image_path: 图片路径
        method: 分析方法
        sample_ratio: 采样比例
        saturation_threshold: 饱和度阈值
        verbose: 是否打印详细信息
        
    Returns:
        dict: 分析结果
    """
    analyzer = HueAnalyzer()
    result = analyzer.analyze_image_hue(image_path, method, sample_ratio, saturation_threshold)
    
    if verbose and 'error' not in result:
        print(f"\n=== 图片色调分析结果 ===")
        print(f"文件: {result['image_path']}")
        print(f"尺寸: {result['image_size']}")
        print(f"分析方法: {result['method']}")
        
        if method == 'dominant':
            if result['dominant_hue'] is not None:
                print(f"主导色调: {result['dominant_hue']}° ({result['dominant_color']})")
                print(f"平均饱和度: {result['average_saturation']}")
                print(f"平均明度: {result['average_value']}")
                print(f"有色像素比例: {result['colored_pixel_ratio']}%")
                print("色调分布:")
                for color, percentage in sorted(result['hue_distribution'].items(), 
                                              key=lambda x: x[1], reverse=True):
                    print(f"  {color}: {percentage}%")
            else:
                print("主导色调: 灰色/无色")
        else:  # average
            print(f"平均色调: {result['average_hue']}° ({result['average_color']})")
            print(f"平均饱和度: {result['average_saturation']}")
            print(f"平均明度: {result['average_value']}")
            rgb = result['average_rgb']
            print(f"平均RGB: R={round(rgb['r']*255)}, G={round(rgb['g']*255)}, B={round(rgb['b']*255)}")
            
    elif verbose and 'error' in result:
        print(f"分析失败: {result['error']}")
    
    return result


def analyze_directory(input_dir: str, output_file: Optional[str] = None,
                     method: str = 'dominant', sample_ratio: float = 0.1,
                     saturation_threshold: float = 0.1) -> List[Dict]:
    """
    批量分析目录中的图片色调

    Args:
        input_dir: 输入目录路径
        output_file: 输出JSON文件路径
        method: 分析方法
        sample_ratio: 采样比例
        saturation_threshold: 饱和度阈值

    Returns:
        list: 所有图片的分析结果
    """
    # 支持的图片格式
    supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}

    analyzer = HueAnalyzer()
    results = []

    print(f"开始批量分析目录: {input_dir}")

    # 遍历目录中的文件
    for filename in sorted(os.listdir(input_dir)):
        file_ext = os.path.splitext(filename)[1].lower()

        if file_ext in supported_formats:
            image_path = os.path.join(input_dir, filename)
            print(f"分析: {filename}")

            result = analyzer.analyze_image_hue(image_path, method, sample_ratio, saturation_threshold)
            results.append(result)

            if 'error' not in result:
                if method == 'dominant' and result['dominant_hue'] is not None:
                    print(f"  主导色调: {result['dominant_hue']}° ({result['dominant_color']})")
                elif method == 'average':
                    print(f"  平均色调: {result['average_hue']}° ({result['average_color']})")
                else:
                    print(f"  色调: 灰色/无色")
            else:
                print(f"  错误: {result['error']}")

    # 保存结果到JSON文件
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"\n结果已保存到: {output_file}")

    return results


def create_hue_visualization(result: Dict, output_path: Optional[str] = None):
    """
    创建色调分析的可视化图表

    Args:
        result: 分析结果字典
        output_path: 输出图片路径
    """
    if 'error' in result or result.get('method') != 'dominant':
        print("无法创建可视化：需要主导色调分析结果")
        return

    if result['dominant_hue'] is None:
        print("无法创建可视化：图片主要为灰色/无色")
        return

    # 创建图表
    _, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

    # 色调分布饼图
    colors = list(result['hue_distribution'].keys())
    sizes = list(result['hue_distribution'].values())

    # 为每个颜色分配对应的颜色
    color_map = {
        '红色': '#FF0000', '橙色': '#FF8000', '黄色': '#FFFF00',
        '黄绿色': '#80FF00', '绿色': '#00FF00', '青绿色': '#00FF80',
        '青色': '#00FFFF', '蓝色': '#0080FF', '蓝紫色': '#0000FF',
        '紫色': '#8000FF', '紫红色': '#FF00FF', '粉红色': '#FF0080'
    }

    pie_colors = [color_map.get(color, '#808080') for color in colors]

    ax1.pie(sizes, labels=colors, colors=pie_colors, autopct='%1.1f%%', startangle=90)
    ax1.set_title('色调分布')

    # 色调环形图
    theta = np.linspace(0, 2*np.pi, 360)

    # 创建色调环
    for i, angle in enumerate(theta):
        hue = i  # 0-359度
        rgb = colorsys.hsv_to_rgb(hue/360, 1, 1)
        ax2.plot([angle, angle], [0.8, 1.0], color=rgb, linewidth=2)

    # 标记主导色调
    dominant_hue_rad = np.radians(result['dominant_hue'])
    ax2.plot([dominant_hue_rad, dominant_hue_rad], [0.5, 1.2], 'k-', linewidth=3, label='主导色调')
    ax2.plot(dominant_hue_rad, 1.1, 'ko', markersize=8)

    ax2.set_ylim(0, 1.3)
    ax2.set_theta_zero_location('N')
    ax2.set_theta_direction(-1)
    ax2.set_title(f'色调环 - 主导色调: {result["dominant_hue"]}°')
    ax2.legend()

    plt.tight_layout()

    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"可视化图表已保存到: {output_path}")
    else:
        plt.show()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='图片色调分析工具')
    parser.add_argument('input', help='输入文件或目录路径')
    parser.add_argument('--output', '-o', help='输出JSON文件路径（仅批量处理时使用）')
    parser.add_argument('--method', '-m', choices=['dominant', 'average'], default='dominant',
                       help='分析方法: dominant=主导色调, average=平均色调 (默认: dominant)')
    parser.add_argument('--sample-ratio', '-s', type=float, default=0.1,
                       help='采样比例 (0.1-1.0, 默认: 0.1)')
    parser.add_argument('--saturation-threshold', '-t', type=float, default=0.1,
                       help='饱和度阈值 (0.0-1.0, 默认: 0.1)')
    parser.add_argument('--visualize', '-v', help='创建可视化图表并保存到指定路径')
    parser.add_argument('--quiet', '-q', action='store_true', help='静默模式，不打印详细信息')

    args = parser.parse_args()

    if not os.path.exists(args.input):
        print(f"错误: 输入路径不存在: {args.input}")
        sys.exit(1)

    # 验证参数范围
    if not 0.1 <= args.sample_ratio <= 1.0:
        print("错误: 采样比例必须在0.1-1.0之间")
        sys.exit(1)

    if not 0.0 <= args.saturation_threshold <= 1.0:
        print("错误: 饱和度阈值必须在0.0-1.0之间")
        sys.exit(1)

    if os.path.isfile(args.input):
        # 分析单个文件
        result = analyze_single_image(args.input, args.method, args.sample_ratio,
                                     args.saturation_threshold, not args.quiet)

        if 'error' in result:
            sys.exit(1)

        # 创建可视化
        if args.visualize:
            create_hue_visualization(result, args.visualize)

    elif os.path.isdir(args.input):
        # 批量分析目录
        results = analyze_directory(args.input, args.output, args.method,
                                   args.sample_ratio, args.saturation_threshold)

        # 统计信息
        if not args.quiet:
            successful = [r for r in results if 'error' not in r]
            if successful:
                if args.method == 'dominant':
                    colored_results = [r for r in successful if r['dominant_hue'] is not None]
                    if colored_results:
                        hues = [r['dominant_hue'] for r in colored_results]
                        colors = [r['dominant_color'] for r in colored_results]
                        color_counts = Counter(colors)

                        print(f"\n=== 批量分析统计 ===")
                        print(f"成功分析: {len(successful)} 张图片")
                        print(f"有色图片: {len(colored_results)} 张")
                        print(f"平均色调: {round(np.mean(hues), 1)}°")
                        print("主导颜色分布:")
                        for color, count in color_counts.most_common():
                            print(f"  {color}: {count} 张 ({count/len(colored_results)*100:.1f}%)")
                else:  # average
                    hues = [r['average_hue'] for r in successful]
                    colors = [r['average_color'] for r in successful]
                    color_counts = Counter(colors)

                    print(f"\n=== 批量分析统计 ===")
                    print(f"成功分析: {len(successful)} 张图片")
                    print(f"平均色调: {round(np.mean(hues), 1)}°")
                    print("平均颜色分布:")
                    for color, count in color_counts.most_common():
                        print(f"  {color}: {count} 张 ({count/len(successful)*100:.1f}%)")
    else:
        print(f"错误: 无效的输入路径: {args.input}")
        sys.exit(1)

    print("\n分析完成!")


if __name__ == "__main__":
    main()
