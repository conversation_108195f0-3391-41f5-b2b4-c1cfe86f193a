from PIL import ImageFilter
from PIL import Image
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import Slider
import os

def load_lut_image_17(image):
    image = Image.open(image).convert('RGB')
    # 把GBR的网格变换成BGR的网格
    table = np.array(image).reshape(17, 17, 17, 3).transpose(1, 0, 2, 3)
    # 0～255 -> 0~1
    table = table.astype(np.float32) / 255.0
    return ImageFilter.Color3DLUT(17, table.reshape(-1))

def load_lut_image_64(image):
    image = Image.open(image).convert('RGB')
    # 把B8G64B8R64的网格变换成B64G64R64的网格
    table = np.array(image).reshape(8, 64, 8, 64, 3).transpose(0, 2, 1, 3, 4)
    # 0～255 -> 0~1
    table = table.astype(np.float32) / 255.0
    return ImageFilter.Color3DLUT(64, table.reshape(-1))

def map_slider_to_intensity(slider_value, min_val, max_val):
    """将滑块值(0-100)映射到实际强度范围"""
    # 50对应中性值0
    if slider_value == 50:
        return 0.0
    elif slider_value > 50:
        # 50-100映射到0-max_val
        return (slider_value - 50) / 50.0 * max_val
    else:
        # 0-50映射到min_val-0
        return (slider_value - 50) / 50.0 * abs(min_val)

def blend_images(original, filtered, intensity):
    """根据强度混合原图和滤镜效果"""
    if intensity == 0:
        return original

    # 转换为numpy数组进行混合
    orig_array = np.array(original, dtype=np.float32)
    filt_array = np.array(filtered, dtype=np.float32)

    # 线性混合
    abs_intensity = abs(intensity)
    blended = orig_array * (1 - abs_intensity) + filt_array * abs_intensity

    # 确保值在有效范围内并转换回PIL图像
    blended = np.clip(blended, 0, 255).astype(np.uint8)
    return Image.fromarray(blended)

def apply_color_adjustment(image, temp_intensity, hue_intensity):
    """应用色温和色调调整"""
    result_image = image.copy()

    # 应用色温调整
    if temp_intensity != 0:
        if temp_intensity > 0:
            # 使用max LUT
            temp_lut = load_lut_image_64('luts/color_temperature_max.png')
        else:
            # 使用min LUT
            temp_lut = load_lut_image_64('luts/color_temperature_min.png')

        # 应用LUT并根据强度混合
        filtered_image = result_image.filter(temp_lut)
        result_image = blend_images(result_image, filtered_image, temp_intensity)

    # 应用色调调整
    if hue_intensity != 0:
        if hue_intensity > 0:
            # 使用max LUT
            hue_lut = load_lut_image_17('luts/hue_max.png')
        else:
            # 使用min LUT
            hue_lut = load_lut_image_17('luts/hue_min.png')

        # 应用LUT并根据强度混合
        filtered_image = result_image.filter(hue_lut)
        result_image = blend_images(result_image, filtered_image, hue_intensity)

    return result_image

class HueTempAdjuster:
    def __init__(self, image_path):
        self.original_image = Image.open(image_path).convert('RGB')
        self.current_image = self.original_image.copy()

        # 创建图形和子图
        self.fig, self.ax = plt.subplots(figsize=(12, 8))
        plt.subplots_adjust(bottom=0.25)

        # 显示原始图像
        self.im_display = self.ax.imshow(np.array(self.original_image))
        self.ax.set_title('色温色调调整')
        self.ax.axis('off')

        # 创建滑块
        # 色温滑块 (范围[-1, 1])
        ax_temp = plt.axes([0.2, 0.1, 0.5, 0.03])
        self.temp_slider = Slider(ax_temp, '色温', 0, 100, valinit=50, valfmt='%d')

        # 色调滑块 (范围[-0.5, 0.5])
        ax_hue = plt.axes([0.2, 0.05, 0.5, 0.03])
        self.hue_slider = Slider(ax_hue, '色调', 0, 100, valinit=50, valfmt='%d')

        # 绑定滑块事件
        self.temp_slider.on_changed(self.update_image)
        self.hue_slider.on_changed(self.update_image)

        # 添加重置按钮
        ax_reset = plt.axes([0.8, 0.025, 0.1, 0.04])
        self.reset_button = plt.Button(ax_reset, '重置')
        self.reset_button.on_clicked(self.reset_values)

    def update_image(self, val=None):
        """更新图像显示"""
        # 获取滑块值并映射到实际强度
        temp_value = self.temp_slider.val
        hue_value = self.hue_slider.val

        temp_intensity = map_slider_to_intensity(temp_value, -1.0, 1.0)
        hue_intensity = map_slider_to_intensity(hue_value, -0.5, 0.5)

        # 应用调整
        self.current_image = apply_color_adjustment(
            self.original_image, temp_intensity, hue_intensity
        )

        # 更新显示
        self.im_display.set_array(np.array(self.current_image))
        self.fig.canvas.draw()

        # 显示当前参数值
        self.ax.set_title(f'色温色调调整 - 色温: {temp_intensity:.2f}, 色调: {hue_intensity:.2f}')

    def reset_values(self, event=None):
        """重置滑块值"""
        self.temp_slider.reset()
        self.hue_slider.reset()

    def save_image(self, output_path):
        """保存当前调整后的图像"""
        self.current_image.save(output_path)
        print(f"图像已保存到: {output_path}")

    def show(self):
        """显示调整界面"""
        plt.show()

def adjust_hue_temp_interactive(image_path):
    """交互式色温色调调整"""
    if not os.path.exists(image_path):
        print(f"错误: 图像文件 {image_path} 不存在")
        return None

    # 检查LUT文件是否存在
    lut_files = [
        'luts/color_temperature_max.png',
        'luts/color_temperature_min.png',
        'luts/hue_max.png',
        'luts/hue_min.png'
    ]

    for lut_file in lut_files:
        if not os.path.exists(lut_file):
            print(f"错误: LUT文件 {lut_file} 不存在")
            return None

    adjuster = HueTempAdjuster(image_path)
    adjuster.show()
    return adjuster

# 示例使用
if __name__ == "__main__":
    # 使用示例
    import sys

    if len(sys.argv) > 1:
        image_path = sys.argv[1]
        print(f"正在加载图像: {image_path}")
        adjuster = adjust_hue_temp_interactive(image_path)
        if adjuster:
            print("使用滑块调整色温和色调，关闭窗口后可以保存图像")
            # 调整完成后可以保存图像:
            # adjuster.save_image("adjusted_image.jpg")
    else:
        print("使用方法: python adjust_hue_temp.py <图像路径>")
        print("例如: python adjust_hue_temp.py test_image.jpg")

