#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片裁剪缩放工具
将图片裁剪缩放为1080x1920的尺寸，根据长宽比进行居中裁剪
"""

import os
import sys
from PIL import Image
import argparse


def crop_resize_image(input_path, output_path=None, target_width=1080, target_height=1920):
    """
    将图片裁剪缩放为指定尺寸

    Args:
        input_path (str): 输入图片路径
        output_path (str): 输出图片路径，如果为None则覆盖原文件
        target_width (int): 目标宽度，默认1080
        target_height (int): 目标高度，默认1920

    Returns:
        bool: 处理是否成功
    """
    try:
        # 打开图片
        with Image.open(input_path) as img:
            # 获取原始尺寸
            original_width, original_height = img.size
            print(f"原始尺寸: {original_width}x{original_height}")

            # 计算目标比例
            target_ratio = target_width / target_height
            original_ratio = original_width / original_height

            # 根据比例决定缩放策略
            if original_ratio > target_ratio:
                # 原图更宽，以高度为准缩放，然后裁剪宽度
                new_height = target_height
                new_width = int(original_width * target_height / original_height)
            else:
                # 原图更高，以宽度为准缩放，然后裁剪高度
                new_width = target_width
                new_height = int(original_height * target_width / original_width)

            # 缩放图片
            resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
            print(f"缩放后尺寸: {new_width}x{new_height}")

            # 计算裁剪区域（居中裁剪）
            left = (new_width - target_width) // 2
            top = (new_height - target_height) // 2
            right = left + target_width
            bottom = top + target_height

            # 裁剪图片
            cropped_img = resized_img.crop((left, top, right, bottom))
            print(f"最终尺寸: {cropped_img.size[0]}x{cropped_img.size[1]}")

            # 保存图片
            if output_path is None:
                output_path = input_path

            # 保持原始图片质量
            if img.format == 'JPEG':
                cropped_img.save(output_path, 'JPEG', quality=95, optimize=True)
            else:
                cropped_img.save(output_path)

            print(f"图片已保存到: {output_path}")
            return True

    except Exception as e:
        print(f"处理图片时出错: {e}")
        return False


def process_directory(input_dir, output_dir=None, target_width=1080, target_height=1920):
    """
    批量处理目录中的图片

    Args:
        input_dir (str): 输入目录路径
        output_dir (str): 输出目录路径，如果为None则覆盖原文件
        target_width (int): 目标宽度
        target_height (int): 目标高度
    """
    # 支持的图片格式
    supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}

    # 创建输出目录
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 遍历目录中的文件
    for filename in os.listdir(input_dir):
        file_ext = os.path.splitext(filename)[1].lower()

        if file_ext in supported_formats:
            input_path = os.path.join(input_dir, filename)

            if output_dir:
                output_path = os.path.join(output_dir, filename)
            else:
                output_path = None

            print(f"\n处理文件: {filename}")
            crop_resize_image(input_path, output_path, target_width, target_height)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='图片裁剪缩放工具')
    parser.add_argument('input', help='输入文件或目录路径')
    parser.add_argument('output', help='输出文件或目录路径')
    parser.add_argument('--width', type=int, default=1080, help='目标宽度 (默认: 1080)')
    parser.add_argument('--height', type=int, default=1920, help='目标高度 (默认: 1920)')

    args = parser.parse_args()

    if not os.path.exists(args.input):
        print(f"错误: 输入路径不存在: {args.input}")
        sys.exit(1)

    if os.path.isfile(args.input):
        # 处理单个文件
        print(f"处理单个文件: {args.input}")
        success = crop_resize_image(args.input, args.output, args.width, args.height)
        if not success:
            sys.exit(1)
    elif os.path.isdir(args.input):
        # 处理目录
        print(f"批量处理目录: {args.input}")
        process_directory(args.input, args.output, args.width, args.height)
    else:
        print(f"错误: 无效的输入路径: {args.input}")
        sys.exit(1)

    print("\n处理完成!")


if __name__ == "__main__":
    main()