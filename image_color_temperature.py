#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片色温计算工具
分析图片的色温，支持多种算法和批量处理
"""

import os
import sys
import numpy as np
from PIL import Image
import argparse
import json
from typing import Tuple, Dict, List, Optional


class ColorTemperatureAnalyzer:
    """色温分析器"""
    
    def __init__(self):
        """初始化色温分析器"""
        # 标准光源的色温对应的RGB值 (归一化)
        self.reference_temperatures = {
            2000: (1.0, 0.549, 0.0),      # 烛光
            2700: (1.0, 0.651, 0.259),    # 白炽灯
            3000: (1.0, 0.698, 0.365),    # 暖白光
            3500: (1.0, 0.746, 0.471),    # 中性白光
            4000: (1.0, 0.794, 0.576),    # 冷白光
            5000: (1.0, 0.863, 0.737),    # 日光
            5500: (1.0, 0.906, 0.843),    # 正午阳光
            6000: (1.0, 0.949, 0.949),    # 阴天
            6500: (1.0, 1.0, 1.0),        # 标准日光D65
            7000: (0.949, 0.949, 1.0),    # 阴影
            8000: (0.863, 0.863, 1.0),    # 蓝天
            10000: (0.737, 0.737, 1.0),   # 深蓝天空
        }
    
    def rgb_to_xy(self, r: float, g: float, b: float) -> Tuple[float, float]:
        """
        将RGB值转换为CIE xy色度坐标
        
        Args:
            r, g, b: RGB值 (0-1范围)
            
        Returns:
            tuple: (x, y) 色度坐标
        """
        # 转换为线性RGB
        def gamma_correction(c):
            if c <= 0.04045:
                return c / 12.92
            else:
                return pow((c + 0.055) / 1.055, 2.4)
        
        r_linear = gamma_correction(r)
        g_linear = gamma_correction(g)
        b_linear = gamma_correction(b)
        
        # 转换为XYZ色彩空间
        X = r_linear * 0.4124564 + g_linear * 0.3575761 + b_linear * 0.1804375
        Y = r_linear * 0.2126729 + g_linear * 0.7151522 + b_linear * 0.0721750
        Z = r_linear * 0.0193339 + g_linear * 0.1191920 + b_linear * 0.9503041
        
        # 转换为xy色度坐标
        total = X + Y + Z
        if total == 0:
            return 0.33, 0.33  # 默认白点
        
        x = X / total
        y = Y / total
        
        return x, y
    
    def xy_to_cct(self, x: float, y: float) -> float:
        """
        使用McCamy公式将xy色度坐标转换为相关色温(CCT)
        
        Args:
            x, y: CIE xy色度坐标
            
        Returns:
            float: 色温值(K)
        """
        # McCamy公式
        n = (x - 0.3320) / (0.1858 - y)
        cct = 449 * n**3 + 3525 * n**2 + 6823.3 * n + 5520.33
        
        # 限制在合理范围内
        return max(1000, min(25000, cct))
    
    def calculate_average_rgb(self, image: Image.Image, sample_ratio: float = 0.1) -> Tuple[float, float, float]:
        """
        计算图片的平均RGB值
        
        Args:
            image: PIL图像对象
            sample_ratio: 采样比例，用于加速处理大图片
            
        Returns:
            tuple: 归一化的平均RGB值 (0-1范围)
        """
        # 转换为RGB模式
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # 如果图片太大，进行下采样
        width, height = image.size
        if sample_ratio < 1.0:
            new_width = int(width * sample_ratio)
            new_height = int(height * sample_ratio)
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # 转换为numpy数组
        img_array = np.array(image)
        
        # 计算平均RGB值
        avg_r = np.mean(img_array[:, :, 0]) / 255.0
        avg_g = np.mean(img_array[:, :, 1]) / 255.0
        avg_b = np.mean(img_array[:, :, 2]) / 255.0
        
        return avg_r, avg_g, avg_b
    
    def calculate_weighted_rgb(self, image: Image.Image, sample_ratio: float = 0.1) -> Tuple[float, float, float]:
        """
        计算图片的加权平均RGB值（基于亮度权重）
        
        Args:
            image: PIL图像对象
            sample_ratio: 采样比例
            
        Returns:
            tuple: 归一化的加权平均RGB值 (0-1范围)
        """
        # 转换为RGB模式
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # 下采样
        if sample_ratio < 1.0:
            width, height = image.size
            new_width = int(width * sample_ratio)
            new_height = int(height * sample_ratio)
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # 转换为numpy数组
        img_array = np.array(image, dtype=np.float32) / 255.0
        
        # 计算亮度权重 (使用感知亮度公式)
        luminance = 0.299 * img_array[:, :, 0] + 0.587 * img_array[:, :, 1] + 0.114 * img_array[:, :, 2]
        
        # 避免除零错误
        total_weight = np.sum(luminance)
        if total_weight == 0:
            return self.calculate_average_rgb(Image.fromarray((img_array * 255).astype(np.uint8)))
        
        # 计算加权平均
        weighted_r = np.sum(img_array[:, :, 0] * luminance) / total_weight
        weighted_g = np.sum(img_array[:, :, 1] * luminance) / total_weight
        weighted_b = np.sum(img_array[:, :, 2] * luminance) / total_weight
        
        return weighted_r, weighted_g, weighted_b
    
    def analyze_color_temperature(self, image_path: str, method: str = 'weighted') -> Dict:
        """
        分析图片的色温
        
        Args:
            image_path: 图片路径
            method: 分析方法 ('average' 或 'weighted')
            
        Returns:
            dict: 包含色温分析结果的字典
        """
        try:
            # 打开图片
            with Image.open(image_path) as img:
                # 获取图片信息
                width, height = img.size
                
                # 计算RGB值
                if method == 'weighted':
                    r, g, b = self.calculate_weighted_rgb(img)
                else:
                    r, g, b = self.calculate_average_rgb(img)
                
                # 转换为xy色度坐标
                x, y = self.rgb_to_xy(r, g, b)
                
                # 计算色温
                color_temperature = self.xy_to_cct(x, y)
                
                # 判断色温类型
                temp_type = self.classify_temperature(color_temperature)
                
                # 计算与标准光源的相似度
                closest_standard = self.find_closest_standard(color_temperature)
                
                return {
                    'image_path': image_path,
                    'image_size': f"{width}x{height}",
                    'method': method,
                    'average_rgb': {
                        'r': round(r, 4),
                        'g': round(g, 4),
                        'b': round(b, 4)
                    },
                    'rgb_255': {
                        'r': round(r * 255),
                        'g': round(g * 255),
                        'b': round(b * 255)
                    },
                    'chromaticity': {
                        'x': round(x, 4),
                        'y': round(y, 4)
                    },
                    'color_temperature': round(color_temperature),
                    'temperature_type': temp_type,
                    'closest_standard': closest_standard
                }
                
        except Exception as e:
            return {
                'image_path': image_path,
                'error': str(e)
            }
    
    def classify_temperature(self, temperature: float) -> str:
        """
        根据色温值分类
        
        Args:
            temperature: 色温值(K)
            
        Returns:
            str: 色温类型描述
        """
        if temperature < 2000:
            return "极暖光 (烛光)"
        elif temperature < 3000:
            return "暖光 (白炽灯)"
        elif temperature < 4000:
            return "中性暖光"
        elif temperature < 5000:
            return "中性光"
        elif temperature < 6000:
            return "冷光"
        elif temperature < 7000:
            return "日光"
        elif temperature < 9000:
            return "冷日光"
        else:
            return "极冷光 (蓝天)"
    
    def find_closest_standard(self, temperature: float) -> Dict:
        """
        找到最接近的标准光源
        
        Args:
            temperature: 色温值(K)
            
        Returns:
            dict: 最接近的标准光源信息
        """
        min_diff = float('inf')
        closest_temp = None
        
        for std_temp in self.reference_temperatures.keys():
            diff = abs(temperature - std_temp)
            if diff < min_diff:
                min_diff = diff
                closest_temp = std_temp
        
        return {
            'temperature': closest_temp,
            'difference': round(min_diff),
            'rgb': self.reference_temperatures[closest_temp]
        }


def analyze_single_image(image_path: str, method: str = 'weighted', verbose: bool = True) -> Dict:
    """
    分析单张图片的色温
    
    Args:
        image_path: 图片路径
        method: 分析方法
        verbose: 是否打印详细信息
        
    Returns:
        dict: 分析结果
    """
    analyzer = ColorTemperatureAnalyzer()
    result = analyzer.analyze_color_temperature(image_path, method)
    
    if verbose and 'error' not in result:
        print(f"\n=== 图片色温分析结果 ===")
        print(f"文件: {result['image_path']}")
        print(f"尺寸: {result['image_size']}")
        print(f"分析方法: {result['method']}")
        print(f"平均RGB: R={result['rgb_255']['r']}, G={result['rgb_255']['g']}, B={result['rgb_255']['b']}")
        print(f"色度坐标: x={result['chromaticity']['x']}, y={result['chromaticity']['y']}")
        print(f"色温: {result['color_temperature']}K")
        print(f"色温类型: {result['temperature_type']}")
        print(f"最接近标准光源: {result['closest_standard']['temperature']}K (差值: {result['closest_standard']['difference']}K)")
    elif verbose and 'error' in result:
        print(f"分析失败: {result['error']}")
    
    return result


def analyze_directory(input_dir: str, output_file: Optional[str] = None, method: str = 'weighted') -> List[Dict]:
    """
    批量分析目录中的图片色温
    
    Args:
        input_dir: 输入目录路径
        output_file: 输出JSON文件路径
        method: 分析方法
        
    Returns:
        list: 所有图片的分析结果
    """
    # 支持的图片格式
    supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
    
    analyzer = ColorTemperatureAnalyzer()
    results = []
    
    print(f"开始批量分析目录: {input_dir}")
    
    # 遍历目录中的文件
    for filename in sorted(os.listdir(input_dir)):
        file_ext = os.path.splitext(filename)[1].lower()
        
        if file_ext in supported_formats:
            image_path = os.path.join(input_dir, filename)
            print(f"分析: {filename}")
            
            result = analyzer.analyze_color_temperature(image_path, method)
            results.append(result)
            
            if 'error' not in result:
                print(f"  色温: {result['color_temperature']}K ({result['temperature_type']})")
            else:
                print(f"  错误: {result['error']}")
    
    # 保存结果到JSON文件
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"\n结果已保存到: {output_file}")
    
    return results


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='图片色温计算工具')
    parser.add_argument('input', help='输入文件或目录路径')
    parser.add_argument('--output', '-o', help='输出JSON文件路径（仅批量处理时使用）')
    parser.add_argument('--method', '-m', choices=['average', 'weighted'], default='weighted',
                       help='分析方法: average=简单平均, weighted=亮度加权平均 (默认: weighted)')
    parser.add_argument('--quiet', '-q', action='store_true', help='静默模式，不打印详细信息')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input):
        print(f"错误: 输入路径不存在: {args.input}")
        sys.exit(1)
    
    if os.path.isfile(args.input):
        # 分析单个文件
        result = analyze_single_image(args.input, args.method, not args.quiet)
        if 'error' in result:
            sys.exit(1)
    elif os.path.isdir(args.input):
        # 批量分析目录
        results = analyze_directory(args.input, args.output, args.method)
        
        # 统计信息
        if not args.quiet:
            successful = [r for r in results if 'error' not in r]
            if successful:
                temperatures = [r['color_temperature'] for r in successful]
                print(f"\n=== 批量分析统计 ===")
                print(f"成功分析: {len(successful)} 张图片")
                print(f"平均色温: {round(np.mean(temperatures))}K")
                print(f"色温范围: {min(temperatures)}K - {max(temperatures)}K")
    else:
        print(f"错误: 无效的输入路径: {args.input}")
        sys.exit(1)
    
    print("\n分析完成!")


if __name__ == "__main__":
    main()
