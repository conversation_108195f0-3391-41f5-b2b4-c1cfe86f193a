# 图片色调分析工具

这是一个用于分析图片色调的Python工具，可以计算图片的主导色调或平均色调，支持单张图片分析和批量处理。

## 功能特点

- **主导色调分析**: 分析图片中占比最大的色调
- **平均色调分析**: 计算图片的整体平均色调
- **色调分类**: 将色调分为12个主要颜色类别
- **批量处理**: 支持目录中多张图片的批量分析
- **可视化**: 生成色调分布饼图和色调环形图
- **灵活配置**: 可调节采样比例和饱和度阈值

## 安装依赖

```bash
pip install pillow numpy matplotlib
```

## 使用方法

### 基本用法

分析单张图片的主导色调：
```bash
python image_hue_analyzer.py test_imgs/sample.jpg
```

分析单张图片的平均色调：
```bash
python image_hue_analyzer.py test_imgs/sample.jpg --method average
```

### 高级选项

指定采样比例（提高处理速度）：
```bash
python image_hue_analyzer.py test_imgs/sample.jpg --sample-ratio 0.2
```

调整饱和度阈值（过滤灰色像素）：
```bash
python image_hue_analyzer.py test_imgs/sample.jpg --saturation-threshold 0.2
```

生成可视化图表：
```bash
python image_hue_analyzer.py test_imgs/sample.jpg --visualize hue_chart.png
```

### 批量处理

分析整个目录：
```bash
python image_hue_analyzer.py test_imgs/ --output results.json
```

静默模式批量处理：
```bash
python image_hue_analyzer.py test_imgs/ --output results.json --quiet
```

## 参数说明

- `input`: 输入文件或目录路径
- `--method, -m`: 分析方法
  - `dominant`: 主导色调分析（默认）
  - `average`: 平均色调分析
- `--sample-ratio, -s`: 采样比例 (0.1-1.0，默认0.1)
- `--saturation-threshold, -t`: 饱和度阈值 (0.0-1.0，默认0.1)
- `--output, -o`: 输出JSON文件路径（批量处理时）
- `--visualize, -v`: 创建可视化图表并保存路径
- `--quiet, -q`: 静默模式

## 输出格式

### 主导色调分析结果

```json
{
  "image_path": "test_imgs/sample.jpg",
  "image_size": "1920x1080",
  "method": "dominant",
  "dominant_hue": 210.5,
  "dominant_color": "蓝色",
  "hue_distribution": {
    "蓝色": 45.2,
    "青色": 23.1,
    "绿色": 15.7,
    "其他": 16.0
  },
  "average_saturation": 0.654,
  "average_value": 0.789,
  "colored_pixel_ratio": 87.3
}
```

### 平均色调分析结果

```json
{
  "image_path": "test_imgs/sample.jpg",
  "image_size": "1920x1080",
  "method": "average",
  "average_hue": 195.2,
  "average_saturation": 0.432,
  "average_value": 0.678,
  "average_color": "青色",
  "average_rgb": {
    "r": 0.5234,
    "g": 0.6789,
    "b": 0.7891
  }
}
```

## 色调分类

工具将色调分为以下12个类别：

| 色调范围 | 颜色名称 | 描述 |
|---------|---------|------|
| 0°-15°, 345°-360° | 红色 | 纯红色调 |
| 15°-45° | 橙色 | 橙红色调 |
| 45°-75° | 黄色 | 黄色调 |
| 75°-105° | 黄绿色 | 黄绿色调 |
| 105°-135° | 绿色 | 纯绿色调 |
| 135°-165° | 青绿色 | 青绿色调 |
| 165°-195° | 青色 | 青色调 |
| 195°-225° | 蓝色 | 纯蓝色调 |
| 225°-255° | 蓝紫色 | 蓝紫色调 |
| 255°-285° | 紫色 | 紫色调 |
| 285°-315° | 紫红色 | 紫红色调 |
| 315°-345° | 粉红色 | 粉红色调 |

## 算法说明

### 主导色调算法
1. 将图片转换为HSV颜色空间
2. 过滤掉饱和度低于阈值的像素（灰色像素）
3. 统计各色调范围的像素数量
4. 找出占比最大的色调类别
5. 计算该类别的平均色调值

### 平均色调算法
1. 计算图片所有像素的RGB平均值
2. 将平均RGB值转换为HSV
3. 根据色调值进行分类

## 性能优化

- 使用采样比例减少处理的像素数量
- 对大图片自动进行下采样
- 支持多种图片格式：JPG, PNG, BMP, TIFF, WebP

## 示例用法

```python
from image_hue_analyzer import HueAnalyzer, analyze_single_image

# 创建分析器
analyzer = HueAnalyzer()

# 分析单张图片
result = analyze_single_image('test.jpg', method='dominant')
print(f"主导色调: {result['dominant_color']} ({result['dominant_hue']}°)")

# 批量分析
from image_hue_analyzer import analyze_directory
results = analyze_directory('images/', 'results.json')
```

## 注意事项

1. 饱和度阈值影响灰色像素的过滤，较高的阈值会过滤更多像素
2. 采样比例影响处理速度和精度，建议在0.1-0.3之间
3. 可视化功能需要matplotlib库
4. 批量处理时建议使用静默模式以减少输出信息

## 与色温分析的区别

- **色调分析**: 关注颜色的基本属性（红、绿、蓝等）
- **色温分析**: 关注光源的冷暖特性（以开尔文为单位）

两个工具可以配合使用，提供图片颜色的全面分析。
